---
import Layout from '@/layouts/Layout.astro';
import ColoringCard from '@/components/ColoringCard.astro';
import Breadcrumb from '@/components/Breadcrumb.astro';
import { getTagsWithCounts, getColoringPagesByTag } from '@/data/coloringPages';
import type { ColoringPage } from '@/types/coloringPage';

// 定义标签类型
interface Tag {
  name: string;
  count: number;
  slug: string;
}

// 启用预渲染
export const prerender = true;

// 定义静态路径
export async function getStaticPaths() {
  // 获取所有标签
  const tags = await getTagsWithCounts();

  // 只为有多个内容的标签创建页面
  const filteredTags = tags.filter(tag => tag.count > 1);

  return filteredTags.map((tag: Tag) => ({
    params: { tag: tag.slug },
    props: { tagName: tag.name, tagCount: tag.count },
  }));
}

// 从 props 中获取标签信息
const { tag } = Astro.params;
const { tagName, tagCount } = Astro.props;

// 获取标签对应的着色页面
const coloringPages = await getColoringPagesByTag(tagName);

// 不再需要预先获取下载链接，CloudflareColoringCard组件会自动处理
const coloringPagesWithUrls = coloringPages;
---

<Layout
  title={`${tagName} Coloring Pages - PrintableColoringHub`}
  description={`Browse our collection of ${tagName} coloring pages. Download free PDF and PNG formats.`}
>
  <div class="container-custom py-8">
    <div class="mb-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Tags", href: "/tags" },
          { label: tagName, isActive: true }
        ]}
      />
    </div>

    <div class="flex items-center gap-3 mb-6">
      <h1 class="mb-0">{tagName} Coloring Pages</h1>
      <span class="tag tag-primary">
        <span>{tagName}</span>
        <span class="tag-count">{tagCount}</span>
      </span>
    </div>

    <p class="text-lg text-gray-700 mb-8">
      Browse our collection of {tagCount} free printable coloring pages tagged with "{tagName}".
      Click on any image to view details and download options.
    </p>

    {coloringPagesWithUrls.length > 0 ? (
      <div class="fixed-4-cols-grid gap-6">
        {coloringPagesWithUrls.map((page) => (
          <ColoringCard
            id={page.id}
            slug={page.slug}
            title={page.title}
            assetFolder={page.assetFolder}
            categoryInfo={page.categoryInfo}
            tags={page.tags}
            isFree={!page.premium}
          />
        ))}
      </div>
    ) : (
      <div class="text-center py-12">
        <p class="text-xl text-gray-600">No coloring pages found with this tag.</p>
        <div class="flex flex-col sm:flex-row justify-center gap-4 mt-4">
          <a href="/" class="btn btn-primary">Back to Home</a>
          <a href="/all-pages" class="btn btn-accent">View All Pages</a>
        </div>
      </div>
    )}
  </div>
</Layout>

<style>
  /* 固定4列网格 - 卡片尺寸根据容器宽度自动调整 */
  .fixed-4-cols-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  /* 在较小屏幕上调整列数 */
  @media (max-width: 640px) {
    .fixed-4-cols-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
    }
  }

  @media (min-width: 641px) and (max-width: 768px) {
    .fixed-4-cols-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.25rem;
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .fixed-4-cols-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 1.5rem;
    }
  }

  /* 1024px以上固定4列 */
  @media (min-width: 1025px) {
    .fixed-4-cols-grid {
      grid-template-columns: repeat(4, 1fr);
      gap: 1.5rem;
    }
  }
</style>
